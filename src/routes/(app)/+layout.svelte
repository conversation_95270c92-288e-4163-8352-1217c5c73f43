<script lang="ts">
	import type { LayoutData } from './$types';
	import type { Snippet } from 'svelte';
	import {
		Sidebar,
		SidebarContent,
		SidebarFooter,
		SidebarGroup,
		SidebarGroupContent,
		SidebarGroupLabel,
		SidebarHeader,
		SidebarInset,
		SidebarMenu,
		SidebarMenuButton,
		SidebarMenuItem,
		SidebarProvider,
		SidebarTrigger
	} from '$lib/components/ui/sidebar';
	import { Button } from '$lib/components/ui/button';
	import { 
		LayoutDashboard,
		Briefcase,
		Users,
		Target,
		BarChart3,
		Settings,
		LogOut,
		ChevronUp,
		MoreHorizontal
	} from 'lucide-svelte';
	import { browser } from '$app/environment';
	import type { ComponentType } from 'svelte';
	import type { Icon } from 'lucide-svelte';

	let { data, children }: { data: LayoutData; children: Snippet } = $props();

	// Initialize sidebar to collapsed state by default
	let sidebarOpen = $state(false);

	if (browser) {
		// Read sidebar state from cookie
		const cookieValue = document.cookie
			.split('; ')
			.find(row => row.startsWith('sidebar:state='))
			?.split('=')[1];
		sidebarOpen = cookieValue === 'true';
	}

	// Get user initials for avatar
	function getUserInitials(user: any): string {
		if (user?.name) {
			return user.name.split(' ').map((n: string) => n[0]).join('').substring(0, 2).toUpperCase();
		}
		if (user?.primaryEmail) {
			return user.primaryEmail.substring(0, 2).toUpperCase();
		}
		return 'U';
	}

	function handleSignOut() {
		window.location.href = '/signout';
	}

	// Navigation items with proper Lucide icons
	type NavigationItem = {
		title: string;
		url: string;
		icon: ComponentType<Icon>;
	};

	const navigationItems: NavigationItem[] = [
		{
			title: 'Dashboard',
			url: '/dashboard',
			icon: LayoutDashboard
		},
		{
			title: 'Jobs',
			url: '/jobs',
			icon: Briefcase
		},
		{
			title: 'Candidates',
			url: '/candidates',
			icon: Users
		},
		{
			title: 'Interviews',
			url: '/interviews',
			icon: Target
		},
		{
			title: 'Reports',
			url: '/reports',
			icon: BarChart3
		},
		{
			title: 'Settings',
			url: '/settings',
			icon: Settings
		}
	];
</script>

<SidebarProvider bind:open={sidebarOpen}>
	<Sidebar collapsible="icon">
		<SidebarHeader>
			<SidebarMenu>
				<SidebarMenuItem>
					<SidebarMenuButton size="lg" asChild>
						<a href="/dashboard">
							<div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
								<span class="text-sm font-semibold">SF</span>
							</div>
							<div class="grid flex-1 text-left text-sm leading-tight">
								<span class="truncate font-semibold">SourceFlex</span>
							</div>
						</a>
					</SidebarMenuButton>
				</SidebarMenuItem>
			</SidebarMenu>
		</SidebarHeader>

		<SidebarContent>
			<SidebarGroup>
				<SidebarGroupLabel>Navigation</SidebarGroupLabel>
				<SidebarGroupContent>
					<SidebarMenu>
						{#each navigationItems as item}
							{@const Icon = item.icon}
							<SidebarMenuItem>
								<SidebarMenuButton asChild>
									<a href={item.url}>
										<Icon />
										<span>{item.title}</span>
									</a>
								</SidebarMenuButton>
							</SidebarMenuItem>
						{/each}
					</SidebarMenu>
				</SidebarGroupContent>
			</SidebarGroup>
		</SidebarContent>

		<SidebarFooter>
			<SidebarMenu>
				<SidebarMenuItem>
					<SidebarMenuButton 
						size="lg"
						on:click={handleSignOut}
						class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
					>
						<div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
							<span class="text-sm font-semibold">{getUserInitials(data.user)}</span>
						</div>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-semibold">{data.user?.name || 'User'}</span>
							<span class="truncate text-xs">{data.user?.primaryEmail || ''}</span>
						</div>
						<MoreHorizontal class="ml-auto size-4" />
					</SidebarMenuButton>
				</SidebarMenuItem>
			</SidebarMenu>
		</SidebarFooter>
	</Sidebar>

	<SidebarInset>
		<header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
			<div class="flex items-center gap-2 px-4">
				<SidebarTrigger class="-ml-1" />
				<div class="h-4 w-px bg-sidebar-border ml-2"></div>
				<nav class="flex items-center gap-2 text-sm text-muted-foreground">
					<a href="/dashboard" class="font-medium text-foreground">Dashboard</a>
				</nav>
			</div>
			
			<!-- Right side actions -->
			<div class="ml-auto flex items-center gap-2 px-4">
				<Button variant="outline" size="sm" class="gap-2">
					<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="M12 5v14m-7-7h14"/>
					</svg>
					Create
				</Button>
			</div>
		</header>

		<main class="flex flex-1 flex-col gap-4 p-4 pt-0">
			{@render children?.()}
		</main>
	</SidebarInset>
</SidebarProvider>

<style>
	:global(.sidebar-wrapper) {
		min-height: 100vh;
		background: hsl(var(--background));
		font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
