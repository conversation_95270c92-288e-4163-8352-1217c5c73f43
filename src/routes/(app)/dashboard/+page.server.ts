import type { PageServerLoad, Actions } from './$types';
import { redirect } from '@sveltejs/kit';
import { DatabaseService } from '$lib/graphql/database-service';

export const load: PageServerLoad = async ({ locals }) => {
	// Ensure user is authenticated
	if (!locals.user) {
		throw redirect(302, '/signin');
	}

	try {
		// Initialize database service
		const dbService = new DatabaseService();

		// Get the database user by Logto ID first
		const logtoUserId = locals.user.sub || locals.user.id;
		if (!logtoUserId) {
			console.error('Logto User ID is undefined:', locals.user);
			throw new Error('Logto User ID is required but not found');
		}
		
		// Get database user
		const dbUser = await dbService.getUserByLogtoId(logtoUserId);
		if (!dbUser) {
			console.error('Database user not found for Logto ID:', logtoUserId);
			// Return empty organizations if user not in database yet
			return {
				user: locals.user,
				organizations: []
			};
		}

		// Get user's organizations using database user ID
		const userOrganizations = await dbService.getUserOrganizations(dbUser.id);

		// Transform the data to include roles in the organization object
		const organizations = userOrganizations?.map((userOrg: {
			organization: {
				id: string;
				name: string;
				slug: string;
				description?: string;
				organization_type?: string;
				is_active?: boolean;
			};
			role: string;
			status?: string;
			joined_at?: string;
		}) => ({
			...userOrg.organization,
			roles: [userOrg.role],
			status: userOrg.status,
			joined_at: userOrg.joined_at
		})) || [];

		return {
			user: locals.user,
			organizations
		};
	} catch (error) {
		console.error('Error loading dashboard data:', error);
		
		// Return user data even if organizations fail to load
		return {
			user: locals.user,
			organizations: []
		};
	}
};

export const actions: Actions = {
	signOut: async ({ locals, url }) => {
		const postLogoutRedirectUri = `${url.origin}/`;
		await locals.logtoClient.signOut(postLogoutRedirectUri);
	}
};