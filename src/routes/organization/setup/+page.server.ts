import type { Actions, PageServerLoad } from './$types';
import { redirect, fail } from '@sveltejs/kit';
import { DatabaseService } from '$lib/graphql/database-service';
import { LOGTO_ADMIN_ENDPOINT, LOGTO_M2M_APP_ID, LOGTO_M2M_APP_SECRET } from '$env/static/private';
import { z } from 'zod';
import { isValidUser, getUserEmail } from '$lib/types/auth';

const createOrgSchema = z.object({
	orgName: z.string().min(1, 'Organization name is required').max(100),
	orgType: z.enum(['hiring', 'bench_sales', 'both']),
	description: z.string().max(500).optional(),
	emailDomain: z.string().optional()
});

const joinOrgSchema = z.object({
	joinCode: z.string().min(1, 'Join code or domain is required')
});

export const load: PageServerLoad = async ({ locals }) => {
	// Ensure user is authenticated
	if (!locals.user || !isValidUser(locals.user)) {
		throw redirect(302, '/signin');
	}

	// TODO: Check if user already has organizations
	// TODO: Check for pending invitations
	// TODO: Check if user's email domain allows auto-join

	return {
		user: locals.user
	};
};

export const actions: Actions = {
	createOrganization: async ({ request, locals }) => {
		console.log('Create organization action called');
		
		if (!locals.user || !isValidUser(locals.user)) {
			throw redirect(302, '/signin');
		}

		const userId = locals.user.sub as string;

		try {
			const formData = await request.formData();
			const data = {
				orgName: formData.get('orgName'),
				orgType: formData.get('orgType'),
				description: formData.get('description'),
				emailDomain: formData.get('emailDomain')
			};

			// Validate form data
			const validatedData = createOrgSchema.parse(data);
			console.log('Validated organization data:', validatedData);

			// Helper function to get access token for Logto Management API
			const getManagementApiToken = async () => {
				const tokenEndpoint = `${LOGTO_ADMIN_ENDPOINT}/oidc/token`;
				const response = await fetch(tokenEndpoint, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
						'Authorization': `Basic ${Buffer.from(`${LOGTO_M2M_APP_ID}:${LOGTO_M2M_APP_SECRET}`).toString('base64')}`
					},
					body: new URLSearchParams({
						grant_type: 'client_credentials',
						resource: `${LOGTO_ADMIN_ENDPOINT}/api`,
						scope: 'all'
					}).toString()
				});

				if (!response.ok) {
					throw new Error(`Failed to get management API token: ${response.statusText}`);
				}

				const tokenData = await response.json();
				return tokenData.access_token;
			};

			// Get access token for management API
			const accessToken = await getManagementApiToken();

			// Create organization in Logto using management API
			const createOrgResponse = await fetch(`${LOGTO_ADMIN_ENDPOINT}/api/organizations`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					name: validatedData.orgName,
					description: validatedData.description || `${validatedData.orgType} organization`
				})
			});

			if (!createOrgResponse.ok) {
				throw new Error(`Failed to create organization in Logto: ${createOrgResponse.statusText}`);
			}

			const logtoOrg = await createOrgResponse.json();

			console.log('Created Logto organization:', logtoOrg);

			// Initialize database service
			const dbService = new DatabaseService();

			// Create organization in database
			const dbOrg = await dbService.insertOrganization({
				logto_id: logtoOrg.id,
				name: validatedData.orgName,
				org_type: validatedData.orgType,
				description: validatedData.description,
				settings: {
					features: {
						hiring: validatedData.orgType === 'hiring' || validatedData.orgType === 'both',
						bench_sales: validatedData.orgType === 'bench_sales' || validatedData.orgType === 'both'
					}
				}
			});

			console.log('Created database organization:', dbOrg);

			// Add email domain if provided
			if (validatedData.emailDomain) {
				await dbService.insertOrganizationDomain({
					organization_id: dbOrg.id,
					domain: validatedData.emailDomain.toLowerCase(),
					auto_join_enabled: true
				});
				console.log('Added email domain:', validatedData.emailDomain);
			}

			// Add user as admin to organization in Logto using Management API
			const addUserResponse = await fetch(`${LOGTO_ADMIN_ENDPOINT}/api/organizations/${logtoOrg.id}/users`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					userIds: [userId]
				})
			});

			if (!addUserResponse.ok) {
				console.warn(`Failed to add user to Logto organization: ${addUserResponse.statusText}`);
				// Continue with database setup even if Logto org membership fails
			} else {
				console.log('User added to Logto organization');
			}

			// TODO: Assign admin role to user in Logto organization (requires organization roles setup)
			// This would require creating organization roles first in Logto

			// Create user-organization relationship in database
			await dbService.insertUserOrganization({
				user_id: userId,
				organization_id: dbOrg.id,
				role: 'admin',
				status: 'active'
			});

			console.log('Successfully created organization and assigned user as admin');
			throw redirect(302, '/dashboard');

		} catch (error) {
			console.error('Error creating organization:', error);
			
			if (error instanceof z.ZodError) {
				return fail(400, {
					error: 'Invalid form data',
					details: error.errors
				});
			}

			return fail(500, {
				error: 'Failed to create organization. Please try again.'
			});
		}
	},

	joinOrganization: async ({ request, locals }) => {
		console.log('Join organization action called');
		
		if (!locals.user || !isValidUser(locals.user)) {
			throw redirect(302, '/signin');
		}

		const userId = locals.user.sub as string;

		try {
			const formData = await request.formData();
			const data = {
				joinCode: formData.get('joinCode')
			};

			// Validate form data
			const validatedData = joinOrgSchema.parse(data);
			console.log('Validated join data:', validatedData);

			const dbService = new DatabaseService();
			const joinCode = validatedData.joinCode.trim();

			// Check if it's an email domain
			if (joinCode.includes('.') && !joinCode.startsWith('INV-')) {
				// Try domain-based auto-join
				const userEmail = getUserEmail(locals.user);
				if (!userEmail) {
					return fail(400, {
						error: 'No email address found in your profile'
					});
				}

				const userDomain = userEmail.split('@')[1]?.toLowerCase();
				if (userDomain !== joinCode.toLowerCase()) {
					return fail(400, {
						error: 'Your email domain does not match the organization domain'
					});
				}

				// Find organization by domain
				const orgDomain = await dbService.getOrganizationByDomain(joinCode.toLowerCase());
				if (!orgDomain || !orgDomain.auto_join_enabled) {
					return fail(400, {
						error: 'Organization not found or auto-join is not enabled for this domain'
					});
				}

				// TODO: Add user to organization
				console.log('Domain-based join for organization:', orgDomain.organization_id);
				
			} else {
				// Try invitation code
				const invitation = await dbService.getOrganizationInvitation(joinCode);
				if (!invitation || invitation.status !== 'pending') {
					return fail(400, {
						error: 'Invalid or expired invitation code'
					});
				}

				// Check if invitation is for this user
				if (invitation.email && invitation.email !== getUserEmail(locals.user)) {
					return fail(400, {
						error: 'This invitation is not for your email address'
					});
				}

				// TODO: Add user to organization via invitation
				console.log('Invitation-based join for organization:', invitation.organization_id);
			}

			// TODO: Complete the join process
			// 1. Add user to organization in Logto
			// 2. Create user-organization relationship in database
			// 3. Update invitation status if applicable
			
			throw redirect(302, '/dashboard');

		} catch (error) {
			console.error('Error joining organization:', error);
			
			if (error instanceof z.ZodError) {
				return fail(400, {
					error: 'Invalid form data',
					details: error.errors
				});
			}

			return fail(500, {
				error: 'Failed to join organization. Please try again.'
			});
		}
	}
};
