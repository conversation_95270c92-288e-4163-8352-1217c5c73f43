import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { dbService } from '$lib/graphql/database-service';

interface OrganizationClaim {
	id: string;
	name: string;
	[key: string]: unknown;
}

export const load: PageServerLoad = async ({ locals, url, cookies }) => {
	console.log('=== CALLBACK DEBUG ===');
	console.log('User authenticated:', !!locals.user);
	
	// Check if user is authenticated using locals.user (Logto's recommended approach)
	if (!locals.user) {
		console.log('No user found, redirecting to signin');
		throw redirect(302, '/signin');
	}
	
	console.log('User info:', {
		id: locals.user.sub || locals.user.id,
		email: locals.user.email || locals.user.primaryEmail,
		name: locals.user.name
	});
	
	// Get redirect target - prioritize cookie, then URL param, then default logic
	const redirectFromCookie = cookies.get('auth_redirect_to');
	const redirectToParam = url.searchParams.get('redirectTo');
	
	console.log('Redirect sources:', {
		cookie: redirectFromCookie,
		urlParam: redirectToParam
	});
	
	let finalRedirect: string;
	
	if (redirectFromCookie) {
		finalRedirect = redirectFromCookie;
		console.log('Using cookie redirect:', finalRedirect);
	} else if (redirectToParam) {
		finalRedirect = redirectToParam;
		console.log('Using URL param redirect:', finalRedirect);
	} else {
		// Default logic - check user's organizations from database
		console.log('Starting database checks...');
		try {
			// Get user from database by Logto ID
			console.log('Attempting to get user from database...');
			let dbUser = await dbService.getUserByLogtoId(locals.user.sub || locals.user.id);
			console.log('Database user result:', dbUser ? 'Found' : 'Not found');
			
			if (!dbUser) {
				console.log('User not found in database, attempting to sync user from Logto');
				
				// User doesn't exist in database, sync them from Logto
				try {
					console.log('Syncing user data:', {
						id: locals.user.sub || locals.user.id,
						email: locals.user.primaryEmail,
						name: locals.user.name
					});
					
					await dbService.syncUser({
						id: locals.user.sub || locals.user.id,
						email: locals.user.primaryEmail,
						username: locals.user.username,
						name: locals.user.name,
						avatar: locals.user.avatar,
						primaryEmail: locals.user.primaryEmail,
						lastSignInAt: Date.now(),
						createdAt: Date.now(),
						updatedAt: Date.now()
					});
					
					// Try to get the user again after sync
					dbUser = await dbService.getUserByLogtoId(locals.user.sub || locals.user.id);
					console.log('User successfully synced to database, retry result:', dbUser ? 'Found' : 'Still not found');
				} catch (syncError) {
					console.error('Failed to sync user to database:', syncError);
					console.error('Sync error details:', syncError);
					// For now, redirect to dashboard even if sync fails
					finalRedirect = '/dashboard';
				}
			}
			
			if (dbUser) {
				console.log('Checking user organizations...');
				// Check user's organization memberships
				const userOrganizations = await dbService.getUserOrganizations(dbUser.id);
				console.log('User organizations from database:', userOrganizations);
				finalRedirect = determineRedirectPath(locals.user, userOrganizations);
			} else {
				// Fallback if user sync failed - still go to dashboard for now
				console.log('User sync failed or user still not found, redirecting to dashboard anyway');
				finalRedirect = '/dashboard';
			}
		} catch (error) {
			console.error('Error checking user organizations:', error);
			console.error('Database error details:', error);
			// Fallback to dashboard for now instead of organization setup
			finalRedirect = '/dashboard';
		}
		
		console.log('Determined redirect based on org status:', finalRedirect);
	}
	
	// Clear the redirect cookie
	cookies.delete('auth_redirect_to', { path: '/' });
	
	console.log('Final redirect destination:', finalRedirect);
	throw redirect(302, finalRedirect);
};

function determineRedirectPath(user: unknown, userOrganizations: any[]): string {
	// Determine redirect based on user's organization status
	if (userOrganizations && userOrganizations.length > 0) {
		// User has organizations, go to dashboard
		console.log('User has organizations, redirecting to dashboard');
		return '/dashboard';
	} else {
		// User has no organizations, needs to create or join one
		console.log('User has no organizations, redirecting to organization setup');
		return '/organization/setup';
	}
}
