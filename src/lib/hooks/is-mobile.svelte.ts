import { browser } from '$app/environment';

/**
 * Hook to detect if the current device is mobile
 */
export class IsMobile {
	private breakpoint: number;
	private mobile = $state(false);

	constructor(breakpoint = 768) {
		this.breakpoint = breakpoint;
		
		if (browser) {
			this.checkMobile();
			window.addEventListener('resize', this.checkMobile);
		}
	}

	private checkMobile = () => {
		if (browser) {
			this.mobile = window.innerWidth < this.breakpoint;
		}
	};

	get current() {
		return this.mobile;
	}

	destroy() {
		if (browser) {
			window.removeEventListener('resize', this.checkMobile);
		}
	}
}