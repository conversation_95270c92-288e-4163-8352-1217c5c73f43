import { getContext, setContext } from 'svelte';
import { IsMobile } from '$lib/hooks/is-mobile.svelte';
import { SIDEBAR_COOKIE_NAME, SIDEBAR_COOKIE_MAX_AGE } from './constants.js';

const SIDEBAR_CONTEXT = 'sidebar';

// Create sidebar context
function createSidebarContext() {
	let open = $state(true);
	let openMobile = $state(false);
	const mobileDetector = new IsMobile();
	let isMobileDevice = $derived(mobileDetector.current);

	function setOpen(value) {
		open = value;
		// Save to cookie
		if (typeof document !== 'undefined') {
			document.cookie = `${SIDEBAR_COOKIE_NAME}=${value}; max-age=${SIDEBAR_COOKIE_MAX_AGE}; path=/`;
		}
	}

	function setOpenMobile(value) {
		openMobile = value;
	}

	function toggle() {
		if (isMobileDevice) {
			setOpenMobile(!openMobile);
		} else {
			setOpen(!open);
		}
	}

	return {
		get open() { return open; },
		get openMobile() { return openMobile; },
		get isMobile() { return isMobileDevice; },
		get state() { return open ? 'expanded' : 'collapsed'; },
		setOpen,
		setOpenMobile,
		toggle
	};
}

// Set sidebar context
export function setSidebar() {
	const context = createSidebarContext();
	setContext(SIDEBAR_CONTEXT, context);
	return context;
}

// Get sidebar context
export function getSidebar() {
	const context = getContext(SIDEBAR_CONTEXT);
	if (!context) {
		throw new Error('getSidebar must be used within a SidebarProvider');
	}
	return context;
}

// Legacy alias for compatibility
export const useSidebar = getSidebar;