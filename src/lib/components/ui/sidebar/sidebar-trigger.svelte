<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';

	import { getSidebar } from './context.svelte.js';

	type $$Props = {
		class?: string;
	};

	let { class: className, ...restProps }: $$Props = $props();

	const { toggle, isMobile } = getSidebar();
</script>

<Button
	variant="ghost"
	size="icon"
	class={cn('h-7 w-7', className)}
	onclick={toggle}
	{...restProps}
>
	<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
		<rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
		<line x1="9" y1="9" x2="15" y2="15"/>
		<line x1="15" y1="9" x2="9" y2="15"/>
	</svg>
	<span class="sr-only">Toggle Sidebar</span>
</Button>
