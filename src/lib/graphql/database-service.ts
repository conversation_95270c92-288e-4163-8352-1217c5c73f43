import { createGraphQLClient } from '$lib/graphql-client';
import type { Client } from '@urql/svelte';

export class DatabaseService {
  private client: Client;

  constructor() {
    this.client = createGraphQLClient();
  }

  /**
   * Sync user data from Logto webhook to our database
   */
  async syncUser(userData: {
    id: string;
    email?: string;
    username?: string;
    name?: string;
    avatar?: string;
    primaryEmail?: string;
    lastSignInAt?: number;
    createdAt: number;
    updatedAt?: number;
  }) {
    // Parse name into first_name and last_name
    const nameParts = userData.name?.split(' ') || [];
    const firstName = nameParts[0] || null;
    const lastName = nameParts.slice(1).join(' ') || null;

    const mutation = `
      mutation UpsertUser($user: users_insert_input!) {
        insert_users_one(
          object: $user
          on_conflict: {
            constraint: users_logto_user_id_key
            update_columns: [
              email, username, first_name, last_name, 
              avatar_url, email_verified, is_active, last_login_at, updated_at
            ]
          }
        ) {
          id
          logto_user_id
          email
          username
          first_name
          last_name
          avatar_url
          email_verified
          is_active
          last_login_at
          created_at
          updated_at
        }
      }
    `;

    const variables = {
      user: {
        logto_user_id: userData.id,
        email: userData.primaryEmail || userData.email || '',
        username: userData.username,
        first_name: firstName,
        last_name: lastName,
        avatar_url: userData.avatar,
        email_verified: true, // Users from Logto are assumed verified
        last_login_at: userData.lastSignInAt ? new Date(userData.lastSignInAt).toISOString() : null,
        created_at: new Date(userData.createdAt).toISOString(),
        updated_at: userData.updatedAt ? new Date(userData.updatedAt).toISOString() : new Date().toISOString()
      }
    };

    const result = await this.client.mutation(mutation, variables).toPromise();
    
    if (result.error) {
      throw new Error(`Failed to sync user: ${result.error.message}`);
    }

    return result.data;
  }

  /**
   * Get user by Logto ID
   */
  async getUserByLogtoId(logtoUserId: string) {
    const query = `
      query GetUserByLogtoId($logtoUserId: String!) {
        users(where: { logto_user_id: { _eq: $logtoUserId } }) {
          id
          logto_user_id
          email
          username
          first_name
          last_name
          avatar_url
          email_verified
          is_active
          last_login_at
          created_at
          updated_at
        }
      }
    `;

    const variables = { logtoUserId };

    const result = await this.client.query(query, variables).toPromise();
    
    if (result.error) {
      throw new Error(`Failed to get user: ${result.error.message}`);
    }

    return result.data?.users[0] || null;
  }

  /**
   * Log webhook event for monitoring and debugging
   */
  async logWebhookEvent(data: {
    webhookType: string;
    logtoEventId?: string;
    payload: Record<string, unknown>;
    status: 'pending' | 'processed' | 'failed' | 'retrying';
    errorMessage?: string;
  }) {
    const mutation = `
      mutation LogWebhookEvent($log: webhook_logs_insert_input!) {
        insert_webhook_logs_one(object: $log) {
          id
          webhook_type
          logto_event_id
          payload
          processing_status
          error_message
          processed_at
          retry_count
          created_at
        }
      }
    `;

    const variables = {
      log: {
        webhook_type: data.webhookType,
        logto_event_id: data.logtoEventId,
        payload: data.payload,
        processing_status: data.status,
        error_message: data.errorMessage,
        processed_at: data.status === 'processed' ? new Date().toISOString() : null,
        created_at: new Date().toISOString()
      }
    };

    const result = await this.client.mutation(mutation, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to log webhook event:', result.error.message);
      // Don't throw here as logging shouldn't break the main flow
    }

    return result.data;
  }

  /**
   * Insert a new organization
   */
  async insertOrganization(data: {
    logto_id: string;
    name: string;
    org_type: string;
    description?: string;
    settings?: Record<string, unknown>;
  }) {
    const mutation = `
      mutation InsertOrganization($org: organizations_insert_input!) {
        insert_organizations_one(object: $org) {
          id
          logto_org_id
          name
          slug
          description
          organization_type
          settings
          is_active
          created_at
          updated_at
        }
      }
    `;

    // Generate slug from name
    const slug = data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');

    const variables = {
      org: {
        logto_org_id: data.logto_id,
        name: data.name,
        slug: slug,
        description: data.description,
        organization_type: data.org_type,
        settings: data.settings || {},
        is_active: true
      }
    };

    const result = await this.client.mutation(mutation, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to insert organization:', result.error.message);
      throw new Error(`Failed to create organization: ${result.error.message}`);
    }

    return result.data?.insert_organizations_one;
  }

  /**
   * Insert organization domain
   */
  async insertOrganizationDomain(data: {
    organization_id: string;
    domain: string;
    auto_join_enabled: boolean;
  }) {
    const mutation = `
      mutation InsertOrganizationDomain($domain: organization_domains_insert_input!) {
        insert_organization_domains_one(object: $domain) {
          id
          organization_id
          domain
          auto_join_enabled
          verified
          created_at
        }
      }
    `;

    const variables = {
      domain: {
        organization_id: data.organization_id,
        domain: data.domain,
        auto_join_enabled: data.auto_join_enabled,
        verified: false
      }
    };

    const result = await this.client.mutation(mutation, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to insert organization domain:', result.error.message);
      throw new Error(`Failed to add organization domain: ${result.error.message}`);
    }

    return result.data?.insert_organization_domains_one;
  }

  /**
   * Insert user-organization relationship
   */
  async insertUserOrganization(data: {
    user_id: string;
    organization_id: string;
    role: string;
    status: string;
  }) {
    const mutation = `
      mutation InsertUserOrganization($userOrg: user_organizations_insert_input!) {
        insert_user_organizations_one(object: $userOrg) {
          id
          user_id
          organization_id
          role
          status
          joined_at
          created_at
        }
      }
    `;

    const variables = {
      userOrg: {
        user_id: data.user_id,
        organization_id: data.organization_id,
        role: data.role,
        status: data.status,
        joined_at: new Date().toISOString()
      }
    };

    const result = await this.client.mutation(mutation, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to insert user organization:', result.error.message);
      throw new Error(`Failed to add user to organization: ${result.error.message}`);
    }

    return result.data?.insert_user_organizations_one;
  }

  /**
   * Get organization by domain
   */
  async getOrganizationByDomain(domain: string) {
    const query = `
      query GetOrganizationByDomain($domain: String!) {
        organization_domains(where: {domain: {_eq: $domain}}) {
          id
          organization_id
          domain
          auto_join_enabled
          verified
          organization {
            id
            name
            logto_org_id
          }
        }
      }
    `;

    const variables = { domain };
    const result = await this.client.query(query, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to get organization by domain:', result.error.message);
      throw new Error(`Failed to find organization: ${result.error.message}`);
    }

    return result.data?.organization_domains?.[0];
  }

  /**
   * Get organization invitation
   */
  async getOrganizationInvitation(token: string) {
    const query = `
      query GetOrganizationInvitation($token: String!) {
        organization_invitations(where: {invitation_token: {_eq: $token}}) {
          id
          organization_id
          email
          role
          status
          expires_at
          invited_by
          created_at
        }
      }
    `;

    const variables = { token };
    const result = await this.client.query(query, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to get organization invitation:', result.error.message);
      throw new Error(`Failed to find invitation: ${result.error.message}`);
    }

    return result.data?.organization_invitations?.[0];
  }

  /**
   * Get user organizations
   */
  async getUserOrganizations(userId: string) {
    const query = `
      query GetUserOrganizations($userId: uuid!) {
        user_organizations(where: {user_id: {_eq: $userId}}) {
          id
          role
          status
          joined_at
          organization {
            id
            name
            slug
            description
            organization_type
            is_active
          }
        }
      }
    `;

    const variables = { userId };
    const result = await this.client.query(query, variables).toPromise();
    
    if (result.error) {
      console.error('Failed to get user organizations:', result.error.message);
      throw new Error(`Failed to get user organizations: ${result.error.message}`);
    }

    return result.data?.user_organizations || [];
  }

  /**
   * Sync user organization membership
   */
  async syncUserOrganizationMembership(data: {
    userId: string;
    organizationId: string;
    role: string;
    status: string;
  }) {
    try {
      const mutation = `
        mutation SyncUserOrganizationMembership($userId: uuid!, $organizationId: uuid!, $role: String!, $status: String!) {
          insert_user_organizations_one(
            object: {
              user_id: $userId
              organization_id: $organizationId
              role: $role
              status: $status
              joined_at: "now()"
            }
            on_conflict: {
              constraint: user_organizations_user_id_organization_id_key
              update_columns: [role, status, updated_at]
            }
          ) {
            id
            user_id
            organization_id
            role
            status
            joined_at
            created_at
            updated_at
          }
        }
      `;

      const variables = {
        userId: data.userId,
        organizationId: data.organizationId,
        role: data.role,
        status: data.status
      };

      const result = await this.client.mutation(mutation, variables).toPromise();
      
      if (result.error) {
        throw new Error(`Failed to sync user organization membership: ${result.error.message}`);
      }

      return result.data?.insert_user_organizations_one || null;
    } catch (error) {
      console.error('Error syncing user organization membership:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const dbService = new DatabaseService();
