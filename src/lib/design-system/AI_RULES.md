# AI Development Rules for SourceFlex
> **MANDATORY PATTERNS** - Always follow these exact patterns for consistent development

## 🎨 Design System Rules

### **Adobe Spectrum Design Tokens (REQUIRED)**
- **Always use `sp-` prefixed CSS variables** from `spectrum-tokens.css`
- **Never hardcode colors, spacing, or typography**
- **Use semantic tokens** (e.g., `--sp-color-accent-content` not `--sp-color-blue-600`)

### **Color Usage Pattern:**
```css
/* ✅ CORRECT - Use semantic tokens */
background-color: var(--sp-color-background-base);
color: var(--sp-color-text-primary);
border-color: var(--sp-color-border-base);

/* ❌ WRONG - Never hardcode colors */
background-color: #ffffff;
color: #2c2c2c;
border-color: #e1e1e1;
```

### **Spacing Pattern:**
```css
/* ✅ CORRECT - Use Spectrum spacing scale */
padding: var(--sp-spacing-400) var(--sp-spacing-600);
margin-bottom: var(--sp-spacing-300);
gap: var(--sp-spacing-200);

/* ❌ WRONG - Never hardcode spacing */
padding: 16px 24px;
margin-bottom: 12px;
gap: 8px;
```

## 🧩 Component Rules (bits-ui + Spectrum)

### **Button Pattern (ALWAYS use this exact structure):**
```svelte
<script lang="ts">
  import { Button } from 'bits-ui';
</script>

<!-- Primary Button -->
<Button.Root 
  class="
    h-[var(--sp-button-height-medium)]
    px-[var(--sp-button-padding-x)]
    bg-[var(--sp-color-accent-content)]
    hover:bg-[var(--sp-color-accent-content-hover)]
    text-[var(--sp-color-text-inverse)]
    rounded-[var(--sp-button-border-radius)]
    font-medium
    transition-colors
  "
>
  {label}
</Button.Root>

<!-- Secondary Button -->
<Button.Root 
  class="
    h-[var(--sp-button-height-medium)]
    px-[var(--sp-button-padding-x)]
    border
    border-[var(--sp-color-border-base)]
    bg-[var(--sp-color-background-base)]
    hover:bg-[var(--sp-color-background-layer-1)]
    text-[var(--sp-color-text-primary)]
    rounded-[var(--sp-button-border-radius)]
    font-medium
    transition-colors
  "
>
  {label}
</Button.Root>
```

### **Form Input Pattern (ALWAYS use this exact structure):**
```svelte
<script lang="ts">
  import { Label } from 'bits-ui';
  import { superForm } from 'sveltekit-superforms';
  import { zodClient } from 'sveltekit-superforms/adapters';
  
  const { form, errors, enhance, constraints } = superForm(data.form, {
    validators: zodClient(schema)
  });
</script>

<form method="POST" use:enhance class="space-y-[var(--sp-spacing-400)]">
  <div>
    <Label.Root 
      for="email" 
      class="
        block 
        text-[var(--sp-color-text-primary)] 
        text-[var(--sp-font-size-100)]
        font-medium
        mb-[var(--sp-spacing-150)]
      "
    >
      Email Address
    </Label.Root>
    
    <input
      id="email"
      name="email"
      type="email"
      bind:value={$form.email}
      aria-invalid={$errors.email ? 'true' : undefined}
      required={$constraints.email?.required}
      class="
        w-full
        h-[var(--sp-field-height-medium)]
        px-[var(--sp-field-padding-x)]
        border
        border-[var(--sp-color-border-base)]
        rounded-[var(--sp-field-border-radius)]
        bg-[var(--sp-color-background-base)]
        text-[var(--sp-color-text-primary)]
        focus:border-[var(--sp-color-accent-content)]
        focus:outline-none
        focus:ring-2
        focus:ring-[var(--sp-color-accent-content)]
        focus:ring-opacity-20
        transition-colors
      "
    />
    
    {#if $errors.email}
      <p class="
        mt-[var(--sp-spacing-150)]
        text-[var(--sp-color-negative)]
        text-[var(--sp-font-size-75)]
      ">
        {$errors.email}
      </p>
    {/if}
  </div>
</form>
```

### **Card Pattern (ALWAYS use this exact structure):**
```svelte
<div class="sp-card">
  <!-- Card Header -->
  <div class="
    border-b 
    border-[var(--sp-color-border-base)]
    pb-[var(--sp-spacing-400)]
    mb-[var(--sp-spacing-600)]
  ">
    <h3 class="sp-heading-md text-[var(--sp-color-text-primary)]">
      Card Title
    </h3>
  </div>
  
  <!-- Card Content -->
  <div class="space-y-[var(--sp-spacing-400)]">
    <!-- Content here -->
  </div>
</div>
```

## 📝 Typography Rules

### **Typography Classes (ALWAYS use these):**
```svelte
<!-- Headings -->
<h1 class="sp-heading-xl text-[var(--sp-color-text-primary)]">Page Title</h1>
<h2 class="sp-heading-lg text-[var(--sp-color-text-primary)]">Section Title</h2>
<h3 class="sp-heading-md text-[var(--sp-color-text-primary)]">Subsection</h3>
<h4 class="sp-heading-sm text-[var(--sp-color-text-primary)]">Small Heading</h4>

<!-- Body Text -->
<p class="sp-body text-[var(--sp-color-text-primary)]">Primary content</p>
<p class="sp-body text-[var(--sp-color-text-secondary)]">Secondary content</p>
<p class="sp-body-sm text-[var(--sp-color-text-secondary)]">Small text</p>
```

## 🏗️ Layout Rules

### **Page Layout Pattern:**
```svelte
<div class="
  max-w-7xl 
  mx-auto 
  px-[var(--sp-spacing-600)] 
  sm:px-[var(--sp-spacing-800)] 
  lg:px-[var(--sp-spacing-1000)] 
  py-[var(--sp-spacing-800)]
">
  <!-- Page content -->
</div>
```

### **Grid Spacing Pattern:**
```svelte
<!-- Card Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[var(--sp-spacing-600)]">
  <!-- Cards -->
</div>

<!-- Content Sections -->
<div class="space-y-[var(--sp-spacing-800)]">
  <!-- Sections -->
</div>
```

## 🎯 Component File Rules

### **File Structure (MANDATORY):**
```
src/lib/components/
├── ui/                    # Spectrum-styled bits-ui components
│   ├── Button.svelte     # Reusable button variants
│   ├── Input.svelte      # Form input component
│   ├── Card.svelte       # Card layout component
│   └── Label.svelte      # Typography label
├── forms/                # Form-specific components
└── layouts/              # Layout components
```

### **Component Import Pattern:**
```svelte
<script lang="ts">
  // Always import bits-ui first
  import { Button, Dialog, Label } from 'bits-ui';
  
  // Then import local components
  import Card from '$lib/components/ui/Card.svelte';
  
  // Then utilities
  import { superForm } from 'sveltekit-superforms';
  import { zodClient } from 'sveltekit-superforms/adapters';
</script>
```

## 🚨 Critical Rules

### **NEVER DO:**
- ❌ Hardcode colors: `bg-blue-600`
- ❌ Hardcode spacing: `p-4`, `m-6`
- ❌ Use random border radius: `rounded-lg`
- ❌ Skip semantic tokens: use `--sp-color-blue-600`
- ❌ Create custom CSS without tokens

### **ALWAYS DO:**
- ✅ Use Spectrum tokens: `bg-[var(--sp-color-accent-content)]`
- ✅ Use Spectrum spacing: `p-[var(--sp-spacing-400)]`
- ✅ Use Spectrum radius: `rounded-[var(--sp-corner-radius-100)]`
- ✅ Use semantic tokens: `--sp-color-accent-content`
- ✅ Apply utility classes: `sp-heading-md`, `sp-card`

### **Component Creation Rules:**
1. **Maximum 200 lines per component** (target: 150 lines)
2. **Use TypeScript** for all props and events
3. **Follow bits-ui patterns** for accessibility
4. **Apply Spectrum tokens** for styling
5. **Include proper error states** with `--sp-color-negative`

## 📋 Checklist for Every Component

Before completing any component, verify:

- [ ] Uses `sp-` prefixed CSS variables only
- [ ] Follows bits-ui component patterns
- [ ] Includes proper TypeScript types
- [ ] Uses semantic color tokens
- [ ] Follows Spectrum spacing scale
- [ ] Includes hover/focus states
- [ ] Includes error states (for forms)
- [ ] Under 200 lines of code
- [ ] Accessible markup with proper ARIA
- [ ] Mobile-responsive design

## 🎨 Spectrum Token Quick Reference

**Most Common Tokens:**
```css
/* Colors */
--sp-color-background-base      /* White/dark background */
--sp-color-text-primary         /* Main text color */
--sp-color-text-secondary       /* Secondary text */
--sp-color-accent-content       /* Primary button color */
--sp-color-negative             /* Error color */
--sp-color-positive             /* Success color */
--sp-color-border-base          /* Default border */

/* Spacing */
--sp-spacing-200    /* 8px - tight spacing */
--sp-spacing-400    /* 16px - default spacing */
--sp-spacing-600    /* 24px - section spacing */
--sp-spacing-800    /* 32px - page spacing */

/* Typography */
--sp-font-size-100  /* 14px - body text */
--sp-font-size-200  /* 16px - large body */
--sp-font-size-400  /* 20px - headings */

/* Border Radius */
--sp-corner-radius-100  /* 4px - buttons/inputs */
--sp-corner-radius-200  /* 8px - cards */
```

---

**🎯 Remember: Consistency is key. Always use these patterns for predictable, professional results.**
